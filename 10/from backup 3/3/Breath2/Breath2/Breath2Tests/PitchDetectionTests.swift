//
//  PitchDetectionTests.swift
//  Breath2Tests
//
//  Created by Mo on 02/06/2025.
//

import XCTest
@testable import Breath2

final class PitchDetectionTests: XCTestCase {
    
    var pitchDetector: PitchDetector!
    var pressureCalculator: PressureCalculator!
    
    override func setUpWithError() throws {
        pitchDetector = PitchDetector(
            sampleRate: 44100.0,
            minFreq: 10,
            maxFreq: 40,
            freqAccuracy: 0.025,
            lowerFormantFreq: 250,
            decayRate: 0.8,
            minAmp: 2.0e-4
        )
        
        pressureCalculator = PressureCalculator()
    }
    
    override func tearDownWithError() throws {
        pitchDetector = nil
        pressureCalculator = nil
    }
    
    func testPitchDetectorInitialization() throws {
        XCTAssertNotNil(pitchDetector)
    }
    
    func testPressureCalculatorInitialization() throws {
        XCTAssertNotNil(pressureCalculator)
    }
    
    func testPressureCalculationLinearModel() throws {
        // Test the linear model: Pressure = 0.5 * Frequency + 2.5
        
        // Test at 15 Hz (should give ~10 cm H2O)
        let pressure15Hz = pressureCalculator.calculatePressure(fromPitch: 15.0)
        XCTAssertEqual(pressure15Hz, 10.0, accuracy: 0.1)
        
        // Test at 35 Hz (should give ~20 cm H2O)
        let pressure35Hz = pressureCalculator.calculatePressure(fromPitch: 35.0)
        XCTAssertEqual(pressure35Hz, 20.0, accuracy: 0.1)
        
        // Test at 25 Hz (should give ~15 cm H2O)
        let pressure25Hz = pressureCalculator.calculatePressure(fromPitch: 25.0)
        XCTAssertEqual(pressure25Hz, 15.0, accuracy: 0.1)
    }
    
    func testFrequencyValidation() throws {
        // Test valid frequencies
        XCTAssertTrue(pressureCalculator.isValidFrequency(15.0))
        XCTAssertTrue(pressureCalculator.isValidFrequency(25.0))
        XCTAssertTrue(pressureCalculator.isValidFrequency(35.0))
        
        // Test invalid frequencies
        XCTAssertFalse(pressureCalculator.isValidFrequency(5.0))  // Too low
        XCTAssertFalse(pressureCalculator.isValidFrequency(45.0)) // Too high
        XCTAssertFalse(pressureCalculator.isValidFrequency(0.0))  // Zero
    }
    
    func testTargetRangeValidation() throws {
        // Test pressures in target range (10-20 cm H2O)
        XCTAssertTrue(pressureCalculator.isInTargetRange(10.0))
        XCTAssertTrue(pressureCalculator.isInTargetRange(15.0))
        XCTAssertTrue(pressureCalculator.isInTargetRange(20.0))

        // Test pressures outside target range
        XCTAssertFalse(pressureCalculator.isInTargetRange(5.0))  // Too low
        XCTAssertFalse(pressureCalculator.isInTargetRange(25.0)) // Too high
    }

    // MARK: - New Paper Implementation Tests

    func testResultSavingConfiguration() throws {
        // Test with result saving enabled
        var config = AlgorithmConfiguration.standard
        config.saveResults = true

        let pitchDetectorWithSaving = PitchDetector(configuration: config)

        // Generate test audio data
        let sampleRate: Float = 44100.0
        let frequency: Float = 20.0 // 20 Hz test frequency
        let duration: Float = 0.2 // 200ms
        let sampleCount = Int(sampleRate * duration)

        var sineWave = [Float](repeating: 0.0, count: sampleCount)
        for i in 0..<sampleCount {
            let time = Float(i) / sampleRate
            sineWave[i] = sin(2.0 * Float.pi * frequency * time) * 0.1
        }

        // Process with proper sample indexing (Paper specification)
        let detectedPitch = pitchDetectorWithSaving.processChunk(sineWave, thisFirstNativeInd: 0, numElements: sampleCount)

        // Export results
        let (detections, correlations) = pitchDetectorWithSaving.exportResults()

        // Verify results were saved
        XCTAssertNotNil(detections, "Detection results should be available when saving is enabled")
        XCTAssertNotNil(correlations, "Correlation data should be available when saving is enabled")

        print("✅ Result saving test completed - Detected pitch: \(detectedPitch) Hz")
    }

    func testEconomicalAutocorrelationWithDerivative() throws {
        // Test the "one wavelength ahead" positioning and derivative-based refinement
        let config = AlgorithmConfiguration.standard
        let detector = PitchDetector(configuration: config)

        // Generate consistent test signal to establish pattern
        let sampleRate: Float = 44100.0
        let frequency: Float = 25.0 // 25 Hz test frequency
        let duration: Float = 0.1 // 100ms chunks
        let sampleCount = Int(sampleRate * duration)

        var sineWave = [Float](repeating: 0.0, count: sampleCount)
        for i in 0..<sampleCount {
            let time = Float(i) / sampleRate
            sineWave[i] = sin(2.0 * Float.pi * frequency * time) * 0.2
        }

        // Process multiple chunks to establish economical search pattern
        var totalSamples = 0
        var lastPitch: Float = 0.0

        for chunk in 0..<5 {
            let detectedPitch = detector.processChunk(sineWave, thisFirstNativeInd: totalSamples, numElements: sampleCount)
            totalSamples += sampleCount

            if detectedPitch > 0 {
                lastPitch = detectedPitch
                print("Chunk \(chunk): Detected \(detectedPitch) Hz at sample \(totalSamples)")
            }
        }

        // Verify economical search is working
        XCTAssertGreaterThan(lastPitch, 0, "Should detect pitch using economical autocorrelation")

        print("✅ Economical autocorrelation test completed - Final pitch: \(lastPitch) Hz")
    }

    func testPipelineStateManagement() throws {
        // Test proper incremental processing with sample indexing
        let config = AlgorithmConfiguration.standard
        let detector = PitchDetector(configuration: config)

        let sampleRate: Float = 44100.0
        let chunkSize = Int(sampleRate * 0.1) // 100ms chunks as per paper

        // Generate test signal
        var totalSamples = 0

        for chunk in 0..<3 {
            var audioChunk = [Float](repeating: 0.0, count: chunkSize)
            let frequency: Float = 20.0 + Float(chunk) * 2.0 // Varying frequency

            for i in 0..<chunkSize {
                let globalTime = Float(totalSamples + i) / sampleRate
                audioChunk[i] = sin(2.0 * Float.pi * frequency * globalTime) * 0.15
            }

            // Process with correct sample indexing (Paper specification)
            let detectedPitch = detector.processChunk(audioChunk, thisFirstNativeInd: totalSamples, numElements: chunkSize)
            totalSamples += chunkSize

            print("Chunk \(chunk): Processed samples \(totalSamples), detected \(detectedPitch) Hz")
        }

        XCTAssertEqual(totalSamples, chunkSize * 3, "Should track total samples correctly")

        print("✅ Pipeline state management test completed")
    }
    
    func testPressureFeedback() throws {
        let feedbackLow = pressureCalculator.getPressureFeedback(5.0)
        XCTAssertTrue(feedbackLow.contains("Too Low"))
        
        let feedbackBelowTarget = pressureCalculator.getPressureFeedback(8.0)
        XCTAssertTrue(feedbackBelowTarget.contains("Below Target"))
        
        let feedbackPerfect = pressureCalculator.getPressureFeedback(15.0)
        XCTAssertTrue(feedbackPerfect.contains("Perfect Range"))
        
        let feedbackAboveTarget = pressureCalculator.getPressureFeedback(22.0)
        XCTAssertTrue(feedbackAboveTarget.contains("Above Target"))
        
        let feedbackHigh = pressureCalculator.getPressureFeedback(28.0)
        XCTAssertTrue(feedbackHigh.contains("Too High"))
    }
    
    func testConfidenceLevel() throws {
        // Test confidence at center frequency (25 Hz)
        let confidenceCenter = pressureCalculator.getConfidenceLevel(25.0)
        XCTAssertEqual(confidenceCenter, 1.0, accuracy: 0.1)
        
        // Test confidence at edge frequencies
        let confidenceEdge1 = pressureCalculator.getConfidenceLevel(10.0)
        XCTAssertLessThan(confidenceEdge1, 1.0)
        
        let confidenceEdge2 = pressureCalculator.getConfidenceLevel(40.0)
        XCTAssertLessThan(confidenceEdge2, 1.0)
        
        // Test confidence for invalid frequency
        let confidenceInvalid = pressureCalculator.getConfidenceLevel(5.0)
        XCTAssertEqual(confidenceInvalid, 0.0)
    }
    
    func testSyntheticSineWave() throws {
        // Generate a synthetic sine wave at 20 Hz
        let frequency: Float = 20.0
        let sampleRate: Float = 44100.0
        let duration: Float = 0.5 // 0.5 seconds
        let amplitude: Float = 0.5
        
        let sampleCount = Int(sampleRate * duration)
        var sineWave = [Float](repeating: 0.0, count: sampleCount)
        
        for i in 0..<sampleCount {
            let t = Float(i) / sampleRate
            sineWave[i] = amplitude * sin(2.0 * Float.pi * frequency * t)
        }
        
        // Process the sine wave through our pitch detector
        let detectedPitch = pitchDetector.processChunk(sineWave)
        
        // The detected pitch should be close to 20 Hz
        // Note: Autocorrelation-based pitch detection may not be perfectly accurate for pure tones
        print("Generated frequency: \(frequency) Hz, Detected frequency: \(detectedPitch) Hz")
        
        // Allow for some tolerance in pitch detection
        if detectedPitch > 0 {
            XCTAssertEqual(detectedPitch, frequency, accuracy: 5.0, "Pitch detection should be within 5 Hz of the generated frequency")
        }
    }
    
    func testModelInfo() throws {
        let modelInfo = pressureCalculator.getModelInfo()
        
        XCTAssertNotNil(modelInfo["slope"])
        XCTAssertNotNil(modelInfo["intercept"])
        XCTAssertNotNil(modelInfo["r_squared"])
        XCTAssertNotNil(modelInfo["data_points"])
        
        // Verify the r² value from the paper
        if let rSquared = modelInfo["r_squared"] as? Double {
            XCTAssertEqual(rSquared, 0.886, accuracy: 0.001)
        }
        
        // Verify the data points count from the paper
        if let dataPoints = modelInfo["data_points"] as? Int {
            XCTAssertEqual(dataPoints, 9993)
        }
    }
}
