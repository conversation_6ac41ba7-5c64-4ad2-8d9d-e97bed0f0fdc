//
//  TherapyView.swift
//  Breath2
//
//  Created by <PERSON> on 02/06/2025.
//

import SwiftUI

struct TherapyView: View {
    @StateObject private var audioManager = AudioManager()
    @ObservedObject var configuration: TherapyConfiguration
    @StateObject private var phaseManager = BreathPhaseManager()
    @EnvironmentObject var historyManager: SessionHistoryManager

    @State private var therapySession: TherapySession
    @State private var showingSessionComplete = false
    @State private var isTransitioning = false
    @State private var showWelcomeScreen = true

    init(configuration: TherapyConfiguration) {
        self.configuration = configuration
        _therapySession = State(initialValue: TherapySession(configuration: configuration))
    }

    // MARK: - Responsive Design Helpers

    /// Adaptive spacing based on screen size - reduced for better fit
    private func adaptiveSpacing(for size: CGSize) -> CGFloat {
        let screenHeight = size.height
        if screenHeight < 700 { // iPhone SE, iPhone 8
            return 12
        } else if screenHeight < 850 { // iPhone 12 mini, iPhone 13 mini
            return 16
        } else if screenHeight < 950 { // iPhone 12, iPhone 13, iPhone 14
            return 18
        } else { // iPhone 12 Pro Max, iPhone 13 Pro Max, iPhone 14 Plus
            return 20
        }
    }

    /// Adaptive padding based on screen width
    private func adaptivePadding(for size: CGSize) -> CGFloat {
        let screenWidth = size.width
        if screenWidth < 380 { // iPhone SE
            return 12
        } else if screenWidth < 400 { // iPhone 12 mini, iPhone 13 mini
            return 16
        } else { // Standard and larger iPhones
            return 20
        }
    }

    /// Dynamic bottom spacing for different screen sizes - reduced to prevent scrolling
    private func bottomSpacing(for size: CGSize) -> CGFloat {
        let screenHeight = size.height
        if screenHeight < 700 { // Smaller screens
            return 80
        } else if screenHeight < 850 { // Medium screens
            return 90
        } else { // Larger screens
            return 100
        }
    }

    /// Adaptive circle size for the progress view
    private func adaptiveCircleSize(for size: CGSize) -> CGFloat {
        let screenWidth = size.width
        let screenHeight = size.height
        let minDimension = min(screenWidth, screenHeight)

        if minDimension < 380 { // iPhone SE
            return 180
        } else if minDimension < 400 { // iPhone 12 mini, iPhone 13 mini
            return 200
        } else if minDimension < 430 { // Standard iPhones
            return 240
        } else { // Larger iPhones
            return 260
        }
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Professional dark background to match tab bar
                LinearGradient(
                    colors: [
                        Color(red: 0.05, green: 0.05, blue: 0.08),  // Very dark blue-gray
                        Color(red: 0.08, green: 0.08, blue: 0.12),  // Slightly lighter dark
                        Color(red: 0.10, green: 0.10, blue: 0.15)   // Dark with subtle blue tint
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()

                if showWelcomeScreen && !therapySession.isActive {
                    // Welcome Screen
                    welcomeScreenView(geometry: geometry)
                        .transition(.asymmetric(
                            insertion: .opacity.combined(with: .scale(scale: 0.8)),
                            removal: .opacity.combined(with: .scale(scale: 1.1))
                        ))
                } else {
                    // Therapy Session Screen
                    therapySessionView(geometry: geometry)
                        .transition(.asymmetric(
                            insertion: .opacity.combined(with: .scale(scale: 0.8)),
                            removal: .opacity.combined(with: .scale(scale: 1.1))
                        ))
                }
            }
        }
        .onAppear {
            audioManager.requestPermission()
            // Check if we need to reset daily progress for a new day
            configuration.checkAndResetIfNewDay()
        }
        .onChange(of: therapySession.isActive) {
            if therapySession.isActive && showWelcomeScreen {
                withAnimation(.spring(response: 0.8, dampingFraction: 0.8, blendDuration: 0.3)) {
                    showWelcomeScreen = false
                }
            } else if !therapySession.isActive && therapySession.currentStep <= 1 {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0.2)) {
                    showWelcomeScreen = true
                }
            }
        }
        .onChange(of: therapySession.currentStep) {
            // Return to welcome screen when session is reset
            if therapySession.currentStep == 1 && !therapySession.isActive {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0.2)) {
                    showWelcomeScreen = true
                }
            }
        }
        .onChange(of: audioManager.currentPressure) { _, newValue in
            therapySession.updatePressure(newValue)
            // Feed data to phase manager for breath-aware UI
            phaseManager.processReading(
                pressure: Float(newValue),
                frequency: Float(audioManager.currentFrequency),
                audioLevel: audioManager.audioLevel
            )
        }
        .onChange(of: audioManager.breathDetector.lastBreathEvent) { _, breathEvent in
            // Handle completed breath events for step performance tracking
            if let event = breathEvent, event.isValid {
                therapySession.processCompletedBreath(event)
            }
        }
        .onChange(of: configuration.dailyBlocks) { _, _ in
            // Update therapy session when configuration changes
            therapySession = TherapySession(configuration: configuration)
        }
        .sheet(isPresented: $showingSessionComplete) {
            SessionCompleteView(session: therapySession)
        }
    }

    // MARK: - Welcome Screen View

    private func welcomeScreenView(geometry: GeometryProxy) -> some View {
        ScrollView(showsIndicators: false) {
            VStack(spacing: 24) {
                // Header section with app icon
                headerSection

                // Today's session summary
                todaysSessionSummary

                // Session blocks overview
                sessionBlocksOverview

                // Today's completed sessions (if any)
                if !historyManager.todaySessions.isEmpty {
                    todaysCompletedSessions
                }

                // Start button section
                startButtonSection

                // Bottom spacing
                Spacer(minLength: 40)
            }
            .padding(.horizontal, adaptivePadding(for: geometry.size))
            .padding(.top, 20)
        }
    }

    // MARK: - Welcome Screen Components

    private var headerSection: some View {
        VStack(spacing: 16) {
            // App icon with breathing animation
            ZStack {
                // Breathing glow effect
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [.cyan.opacity(0.3), .blue.opacity(0.1), .clear],
                            center: .center,
                            startRadius: 20,
                            endRadius: 60
                        )
                    )
                    .frame(width: 100, height: 100)
                    .scaleEffect(isTransitioning ? 1.2 : 1.0)
                    .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: isTransitioning)

                // Main icon
                Image(systemName: "lungs.fill")
                    .font(.system(size: 40, weight: .medium))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.cyan, .blue],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .scaleEffect(isTransitioning ? 1.1 : 1.0)
                    .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: isTransitioning)
            }
            .onAppear {
                isTransitioning = true
            }

            // Title
            VStack(spacing: 8) {
                Text("PEP Therapy")
                    .font(.system(size: 32, weight: .bold, design: .rounded))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)

                // Elegant guidance
                HStack(spacing: 8) {
                    Text("Target the")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.gray)

                    HStack(spacing: 6) {
                        Circle()
                            .fill(Color.green)
                            .frame(width: 10, height: 10)

                        Text("green zone")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.green)
                    }
                }
            }
        }
    }

    private var todaysSessionSummary: some View {
        VStack(spacing: 16) {
            // Summary header
            HStack {
                Text("Today's Progress")
                    .font(.system(size: 20, weight: .bold, design: .rounded))
                    .foregroundColor(.white)
                Spacer()
            }

            // Progress overview card
            VStack(spacing: 16) {
                // Overall progress bar
                VStack(spacing: 8) {
                    HStack {
                        Text("Overall Progress")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.gray)
                        Spacer()
                        Text("\(configuration.completedExhalationsToday)/\(configuration.totalExhalationsToday) breaths")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(.cyan)
                    }

                    ProgressView(value: configuration.progressToday)
                        .progressViewStyle(LinearProgressViewStyle(tint: .cyan))
                        .scaleEffect(x: 1, y: 2, anchor: .center)
                }

                // Session stats
                HStack(spacing: 20) {
                    // Sessions completed
                    VStack(spacing: 4) {
                        Text("\(configuration.sessionsCompletedToday)")
                            .font(.system(size: 24, weight: .bold))
                            .foregroundColor(.green)
                        Text("Completed")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }

                    Divider()
                        .frame(height: 40)

                    // Sessions remaining
                    VStack(spacing: 4) {
                        Text("\(configuration.dailyBlocks.count - configuration.sessionsCompletedToday)")
                            .font(.system(size: 24, weight: .bold))
                            .foregroundColor(.orange)
                        Text("Remaining")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }

                    Divider()
                        .frame(height: 40)

                    // Total time estimate
                    VStack(spacing: 4) {
                        Text(formatTotalTimeEstimate())
                            .font(.system(size: 24, weight: .bold))
                            .foregroundColor(.blue)
                        Text("Est. Time")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                }
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.white.opacity(0.05))
                    .stroke(Color.white.opacity(0.1), lineWidth: 1)
            )
        }
    }

    private var sessionBlocksOverview: some View {
        VStack(spacing: 16) {
            // Sessions header
            HStack {
                Text("Today's Sessions")
                    .font(.system(size: 20, weight: .bold, design: .rounded))
                    .foregroundColor(.white)
                Spacer()
            }

            // Session blocks
            VStack(spacing: 12) {
                ForEach(Array(configuration.dailyBlocks.enumerated()), id: \.element.id) { index, block in
                    sessionBlockCard(block: block, index: index)
                }
            }
        }
    }

    private func sessionBlockCard(block: TherapyBlock, index: Int) -> some View {
        HStack(spacing: 16) {
            // Session status indicator
            ZStack {
                Circle()
                    .fill(sessionStatusColor(for: block, index: index))
                    .frame(width: 40, height: 40)

                if block.isCompleted {
                    Image(systemName: "checkmark")
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.white)
                } else if index == configuration.currentBlockIndex {
                    Image(systemName: "play.fill")
                        .font(.system(size: 14, weight: .bold))
                        .foregroundColor(.white)
                } else {
                    Text("\(index + 1)")
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.white)
                }
            }

            // Session details
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(block.name)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                    Spacer()
                    if block.isCompleted {
                        Text("Completed")
                            .font(.caption)
                            .foregroundColor(.green)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                            .background(Color.green.opacity(0.2))
                            .cornerRadius(8)
                    } else if index == configuration.currentBlockIndex {
                        Text("Next")
                            .font(.caption)
                            .foregroundColor(.cyan)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                            .background(Color.cyan.opacity(0.2))
                            .cornerRadius(8)
                    }
                }

                HStack(spacing: 16) {
                    HStack(spacing: 4) {
                        Image(systemName: "lungs")
                            .font(.caption)
                            .foregroundColor(.gray)
                        Text("\(block.exhalations) breaths")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }

                    HStack(spacing: 4) {
                        Image(systemName: "clock")
                            .font(.caption)
                            .foregroundColor(.gray)
                        Text(block.formattedDuration)
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                }
            }

            Spacer()
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(sessionBackgroundColor(for: block, index: index))
                .stroke(sessionBorderColor(for: block, index: index), lineWidth: 1)
        )
    }

    private var todaysCompletedSessions: some View {
        VStack(spacing: 16) {
            // Completed sessions header
            HStack {
                Text("Completed Today")
                    .font(.system(size: 20, weight: .bold, design: .rounded))
                    .foregroundColor(.white)
                Spacer()
                Text("\(historyManager.todaySessions.count) session\(historyManager.todaySessions.count == 1 ? "" : "s")")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.green)
            }

            // Completed sessions list
            VStack(spacing: 8) {
                ForEach(historyManager.todaySessions.prefix(3)) { session in
                    completedSessionCard(session: session)
                }
            }
        }
    }

    private func completedSessionCard(session: CompletedSession) -> some View {
        HStack(spacing: 12) {
            // Quality indicator
            ZStack {
                Circle()
                    .fill(session.qualityColor)
                    .frame(width: 32, height: 32)

                Image(systemName: session.qualityIcon)
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(.white)
            }

            // Session details
            VStack(alignment: .leading, spacing: 2) {
                HStack {
                    Text(session.date, style: .time)
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.white)
                    Spacer()
                    Text(session.quality.description)
                        .font(.caption)
                        .foregroundColor(session.qualityColor)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(session.qualityColor.opacity(0.2))
                        .cornerRadius(6)
                }

                HStack(spacing: 12) {
                    HStack(spacing: 4) {
                        Image(systemName: "clock")
                            .font(.caption2)
                            .foregroundColor(.gray)
                        Text(session.formattedDuration)
                            .font(.caption)
                            .foregroundColor(.gray)
                    }

                    HStack(spacing: 4) {
                        Image(systemName: "checkmark.circle")
                            .font(.caption2)
                            .foregroundColor(.gray)
                        Text("\(session.stepsCompleted)/\(session.totalSteps)")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                }
            }

            Spacer()
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(Color.white.opacity(0.03))
                .stroke(session.qualityColor.opacity(0.2), lineWidth: 1)
        )
    }

    private var startButtonSection: some View {
        VStack(spacing: 16) {
            if configuration.hasMoreSessions {
                Button(action: {
                    startTherapyWithAnimation()
                }) {
                    HStack(spacing: 12) {
                        Image(systemName: "play.fill")
                            .font(.system(size: 18, weight: .semibold))
                        Text(startButtonText)
                            .font(.system(size: 18, weight: .semibold, design: .rounded))
                    }
                    .foregroundColor(.white)
                    .padding(.vertical, 18)
                    .frame(maxWidth: .infinity)
                    .background(
                        LinearGradient(
                            colors: [.blue, .cyan],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(16)
                    .shadow(color: .blue.opacity(0.4), radius: 12, x: 0, y: 6)
                    .scaleEffect(audioManager.hasPermission ? 1.0 : 0.95)
                    .animation(.easeInOut(duration: 0.2), value: audioManager.hasPermission)
                }
                .disabled(!audioManager.hasPermission)
            } else {
                // All sessions completed
                VStack(spacing: 12) {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 48))
                        .foregroundColor(.green)

                    Text("All Sessions Complete!")
                        .font(.system(size: 20, weight: .bold, design: .rounded))
                        .foregroundColor(.white)

                    Text("Great job! You've completed all therapy sessions for today.")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 20)
                }
                .padding(.vertical, 20)
            }

            if !audioManager.hasPermission && configuration.hasMoreSessions {
                Text("Microphone permission required")
                    .font(.caption)
                    .foregroundColor(.red)
                    .padding(.top, 8)
            }
        }
    }

    // MARK: - Therapy Session View

    private func therapySessionView(geometry: GeometryProxy) -> some View {
        ScrollView(showsIndicators: false) {
            VStack(spacing: adaptiveSpacing(for: geometry.size)) {
                // Header
                headerView
                    .padding(.top, geometry.safeAreaInsets.top > 0 ? 0 : 10)

                // Therapy Progress Steps
                therapyStepsView

                // Main Progress - Consistent Breath-Focused UI
                mainProgressView(geometry: geometry)

                // Control Buttons
                controlButtonsView

                // Dynamic bottom spacing based on screen size
                Spacer(minLength: bottomSpacing(for: geometry.size))
            }
            .padding(.horizontal, adaptivePadding(for: geometry.size))
            .padding(.top, 5)
        }
    }

    // MARK: - Animation Helper

    private func startTherapyWithAnimation() {
        if audioManager.hasPermission {
            withAnimation(.spring(response: 0.8, dampingFraction: 0.8, blendDuration: 0.3)) {
                therapySession.startSession()
                audioManager.startRecording()
            }
        } else {
            audioManager.requestPermission()
        }
    }

    // MARK: - Header View

    private var headerView: some View {
        VStack(spacing: 8) {
            // App icon or breathing symbol
            Image(systemName: "lungs.fill")
                .font(.system(size: 28))
                .foregroundStyle(
                    LinearGradient(
                        colors: [.cyan, .blue],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .padding(.bottom, 2)

            Text("PEP Therapy")
                .font(.system(size: 28, weight: .bold, design: .rounded))
                .foregroundColor(.white)

            Text(therapySession.currentBlockName.isEmpty ? "Therapy Session" : therapySession.currentBlockName)
                .font(.system(size: 16, weight: .medium, design: .rounded))
                .foregroundStyle(
                    LinearGradient(
                        colors: [.cyan, .blue],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
        }
        .padding(.top, 10)
        .padding(.bottom, 5)
    }

    // MARK: - Therapy Steps View
    
    private var therapyStepsView: some View {
        VStack(spacing: 16) {
            TherapyStepView(
                currentStep: therapySession.currentStep,
                totalSteps: therapySession.totalSteps,
                stepPerformances: therapySession.stepPerformances
            )

            // Session info row
            if therapySession.isActive {
                HStack {
                    // Session timer (moved to left)
                    Text(formatDuration(therapySession.sessionDuration))
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                        .padding(.horizontal, 12)
                        .padding(.vertical, 4)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)

                    Spacer()
                }
            }
        }
        .padding(.vertical, 8)
    }
    
    // MARK: - Main Progress View

    private func mainProgressView(geometry: GeometryProxy) -> some View {
        VStack(spacing: 20) {
            // Real-time message area for exhalation guidance
            if therapySession.isActive {
                realTimeMessageArea
            }

            // Circular progress with adaptive sizing
            ZStack {
                let circleSize = adaptiveCircleSize(for: geometry.size)

                // Subtle background circle for dark theme
                Circle()
                    .fill(Color.white.opacity(0.08))
                    .frame(width: circleSize + 40, height: circleSize + 40)
                    .blur(radius: 15)

                // Additional glow effect
                Circle()
                    .fill(Color.cyan.opacity(0.1))
                    .frame(width: circleSize, height: circleSize)
                    .blur(radius: 25)

                BreathCircularProgressView(
                    breathDetector: audioManager.breathDetector,
                    progress: min(audioManager.currentPressure / 30.0, 1.0),
                    currentPressure: audioManager.currentPressure,
                    targetMin: 10,
                    targetMax: 20,
                    maxValue: 30
                )
                .frame(width: circleSize, height: circleSize)
            }
        }
    }

    private var realTimeMessageArea: some View {
        VStack(spacing: 8) {
            // Prominent action message (1-2 words max)
            Text(conciseActionMessage)
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(actionMessageColor)
                .multilineTextAlignment(.center)
                .animation(.easeInOut(duration: 0.3), value: conciseActionMessage)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(actionMessageColor.opacity(0.1))
                        .stroke(actionMessageColor.opacity(0.3), lineWidth: 1)
                )
        }
    }

    private var conciseActionMessage: String {
        if audioManager.currentPressure == 0 {
            return "Breathe"
        } else if audioManager.currentPressure >= 10 && audioManager.currentPressure <= 20 {
            return "Perfect!"
        } else if audioManager.currentPressure < 10 {
            return "Harder"
        } else {
            return "Softer"
        }
    }

    private var actionMessageColor: Color {
        if audioManager.currentPressure == 0 {
            return .cyan
        } else if audioManager.currentPressure >= 10 && audioManager.currentPressure <= 20 {
            return .green
        } else if audioManager.currentPressure < 10 {
            return .orange
        } else {
            return .red
        }
    }

    // MARK: - Control Buttons View

    private var controlButtonsView: some View {
        VStack(spacing: 16) {
            if therapySession.isActive {
                // Active session controls - clean horizontal layout
                HStack(spacing: 16) {
                    // Pause button
                    Button(action: {
                        therapySession.pauseSession()
                        audioManager.stopRecording()
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: "pause.fill")
                                .font(.system(size: 16, weight: .semibold))
                            Text("Pause")
                                .font(.system(size: 16, weight: .semibold, design: .rounded))
                        }
                        .foregroundColor(.white)
                        .padding(.vertical, 14)
                        .frame(maxWidth: .infinity)
                        .background(
                            LinearGradient(
                                colors: [.orange, .yellow],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(14)
                        .shadow(color: .orange.opacity(0.3), radius: 6, x: 0, y: 3)
                    }

                    // End session button
                    Button(action: {
                        therapySession.endSession()
                        audioManager.stopRecording()
                        historyManager.saveSession(therapySession)
                        showingSessionComplete = true
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: "stop.fill")
                                .font(.system(size: 16, weight: .semibold))
                            Text("End Session")
                                .font(.system(size: 16, weight: .semibold, design: .rounded))
                        }
                        .foregroundColor(.white)
                        .padding(.vertical, 14)
                        .frame(maxWidth: .infinity)
                        .background(
                            LinearGradient(
                                colors: [.red, .pink],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(14)
                        .shadow(color: .red.opacity(0.3), radius: 6, x: 0, y: 3)
                    }
                }

            } else if therapySession.currentStep > 1 && !therapySession.isActive {
                // Session paused controls - clean horizontal layout
                HStack(spacing: 16) {
                    Button(action: {
                        therapySession.resumeSession()
                        audioManager.startRecording()
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: "play.fill")
                                .font(.system(size: 16, weight: .semibold))
                            Text("Resume")
                                .font(.system(size: 16, weight: .semibold, design: .rounded))
                        }
                        .foregroundColor(.white)
                        .padding(.vertical, 14)
                        .frame(maxWidth: .infinity)
                        .background(
                            LinearGradient(
                                colors: [.green, .mint],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(14)
                        .shadow(color: .green.opacity(0.3), radius: 6, x: 0, y: 3)
                    }

                    Button(action: {
                        therapySession.endSession()
                        historyManager.saveSession(therapySession)
                        showingSessionComplete = true
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: "stop.fill")
                                .font(.system(size: 16, weight: .semibold))
                            Text("End Session")
                                .font(.system(size: 16, weight: .semibold, design: .rounded))
                        }
                        .foregroundColor(.white)
                        .padding(.vertical, 14)
                        .frame(maxWidth: .infinity)
                        .background(
                            LinearGradient(
                                colors: [.red, .pink],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(14)
                        .shadow(color: .red.opacity(0.3), radius: 6, x: 0, y: 3)
                    }
                }

            } else {
                // Start session controls
                Button(action: {
                    if audioManager.hasPermission {
                        therapySession.startSession()
                        audioManager.startRecording()
                    } else {
                        audioManager.requestPermission()
                    }
                }) {
                    HStack(spacing: 10) {
                        Image(systemName: "play.fill")
                            .font(.system(size: 18, weight: .semibold))
                        Text("Start Therapy")
                            .font(.system(size: 17, weight: .semibold, design: .rounded))
                    }
                    .foregroundColor(.white)
                    .padding(.vertical, 16)
                    .frame(maxWidth: .infinity)
                    .background(
                        LinearGradient(
                            colors: [.blue, .cyan],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(14)
                    .shadow(color: .blue.opacity(0.4), radius: 8, x: 0, y: 4)
                    .scaleEffect(audioManager.hasPermission ? 1.0 : 0.95)
                    .animation(.easeInOut(duration: 0.2), value: audioManager.hasPermission)
                }
                .disabled(!audioManager.hasPermission)

                if !audioManager.hasPermission {
                    Text("Microphone permission required")
                        .font(.caption)
                        .foregroundColor(.red)
                        .padding(.top, 8)
                }
            }
        }
        .padding(.horizontal, 4)
    }

    // MARK: - Helper Functions

    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }

    // MARK: - Welcome Screen Helper Functions

    private func formatTotalTimeEstimate() -> String {
        let remainingBlocks = Array(configuration.dailyBlocks.suffix(from: configuration.currentBlockIndex))
        let totalTime = remainingBlocks.reduce(0) { $0 + $1.estimatedDuration }
        let minutes = Int(totalTime) / 60
        return "\(minutes)m"
    }

    private var startButtonText: String {
        if let currentBlock = configuration.currentBlock {
            return "Start \(currentBlock.name)"
        } else {
            return "Start Therapy"
        }
    }

    private func sessionStatusColor(for block: TherapyBlock, index: Int) -> Color {
        if block.isCompleted {
            return .green
        } else if index == configuration.currentBlockIndex {
            return .cyan
        } else {
            return .gray.opacity(0.6)
        }
    }

    private func sessionBackgroundColor(for block: TherapyBlock, index: Int) -> Color {
        if block.isCompleted {
            return .green.opacity(0.1)
        } else if index == configuration.currentBlockIndex {
            return .cyan.opacity(0.1)
        } else {
            return Color.white.opacity(0.03)
        }
    }

    private func sessionBorderColor(for block: TherapyBlock, index: Int) -> Color {
        if block.isCompleted {
            return .green.opacity(0.3)
        } else if index == configuration.currentBlockIndex {
            return .cyan.opacity(0.3)
        } else {
            return Color.white.opacity(0.1)
        }
    }
}

// MARK: - Session Complete View

struct SessionCompleteView: View {
    @ObservedObject var session: TherapySession
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // Success icon
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 80))
                    .foregroundColor(.green)
                    .padding(.top, 40)

                // Session summary
                VStack(spacing: 12) {
                    Text("Session Complete!")
                        .font(.title)
                        .fontWeight(.bold)

                    Text(session.feedback)
                        .font(.body)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)
                }

                // Session stats
                VStack(spacing: 16) {
                    HStack {
                        VStack(alignment: .leading) {
                            Text("Duration")
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.7))
                            Text(formatDuration(session.sessionDuration))
                                .font(.title3)
                                .fontWeight(.semibold)
                        }

                        Spacer()

                        VStack(alignment: .trailing) {
                            Text("Quality")
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.7))
                            Text(session.sessionQuality.description)
                                .font(.title3)
                                .fontWeight(.semibold)
                                .foregroundColor(session.sessionQuality.color)
                        }
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(12)
                }

                Spacer()

                // Action buttons
                VStack(spacing: 12) {
                    // Next session button (if more sessions available)
                    if session.hasMoreSessionsToday {
                        Button(action: {
                            session.startNextSession()
                            dismiss()
                        }) {
                            HStack(spacing: 8) {
                                Image(systemName: "arrow.right.circle.fill")
                                    .font(.system(size: 18))
                                Text("Next Session")
                                    .font(.headline)
                                    .fontWeight(.medium)
                            }
                            .foregroundColor(.white)
                            .padding(.vertical, 16)
                            .frame(maxWidth: .infinity)
                            .background(
                                LinearGradient(
                                    colors: [.green, .mint],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .cornerRadius(16)
                            .shadow(color: .green.opacity(0.3), radius: 6, x: 0, y: 3)
                        }
                    }

                    Button(action: {
                        // Reset for new session
                        session.resetSession()
                        dismiss()
                    }) {
                        Text("Try Again")
                            .font(.headline)
                            .fontWeight(.medium)
                            .foregroundColor(.blue)
                            .padding(.vertical, 16)
                            .frame(maxWidth: .infinity)
                            .background(Color.blue.opacity(0.1))
                            .cornerRadius(16)
                    }

                    Button(action: {
                        // Session already saved, just reset and dismiss
                        session.resetSession()
                        dismiss()
                    }) {
                        Text(session.hasMoreSessionsToday ? "Finish for Today" : "Done")
                            .font(.headline)
                            .fontWeight(.medium)
                            .foregroundColor(.white)
                            .padding(.vertical, 16)
                            .frame(maxWidth: .infinity)
                            .background(Color.blue)
                            .cornerRadius(16)
                    }
                }
                .padding(.bottom, 40)
            }
            .padding(.horizontal, 24)
            .navigationTitle("Session Results")
            .navigationBarTitleDisplayMode(.inline)
            .toolbarBackground(Color(red: 0.05, green: 0.05, blue: 0.08), for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        session.resetSession()
                        dismiss()
                    }
                }
            }
        }
    }

    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
}

#Preview {
    TherapyView(configuration: TherapyConfiguration())
        .environmentObject(SessionHistoryManager())
}
