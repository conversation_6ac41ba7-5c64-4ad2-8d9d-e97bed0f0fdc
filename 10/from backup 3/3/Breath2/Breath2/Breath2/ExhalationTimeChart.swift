//
// MARK: - Exhalation Time Chart

struct ExhalationTimeChart: View {
    let session: CompletedSession

    // MARK: - Duration Zone Constants
    private let optimalDurationThreshold: Double = 3.0  // ≥3.0s is optimal
    private let maxYAxisValue: Double = 10.0           // Fixed Y-axis maximum
    private let minYAxisValue: Double = 0.0            // Fixed Y-axis minimum

    private var exhalationData: [ExhalationDataPoint] {
        let durations = session.exhalationDurations
        print("🔍 Debug: Raw exhalation durations: \(durations)")
        
        // Ensure we have valid data
        guard !durations.isEmpty else {
            print("⚠️ Warning: No exhalation durations available")
            return []
        }
        
        // Validate and clean data before creating data points
        let validDurations = durations.map { max(minYAxisValue, min($0, maxYAxisValue)) }
        
        let dataPoints = validDurations.enumerated().map { index, duration in
            ExhalationDataPoint(
                exhalationNumber: index + 1,
                duration: duration,
                isOptimal: duration >= optimalDurationThreshold
            )
        }
        
        print("🔍 Debug: Generated data points: \(dataPoints.map { "(\($0.exhalationNumber), \($0.duration))" })")
        return dataPoints
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Chart title and legend
            VStack(alignment: .leading, spacing: 8) {
                Text("Exhalation Duration Compliance")
                    .font(.title3)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                // Add data validation message
                if exhalationData.isEmpty {
                    Text("No data available")
                        .foregroundColor(.red)
                        .font(.caption)
                }
                
                // Legend
                HStack(spacing: 16) {
                    LegendItem(color: .green, label: "≥3.0s (Optimal)")
                    LegendItem(color: .orange, label: "<3.0s (Too Short)")
                }
                .font(.caption)
            }
            
            // Modern chart with zone coloring
            VStack(spacing: 8) {
                Chart {
                    if !exhalationData.isEmpty {
                        let minX = 1
                        let maxX = exhalationData.count
                        
                        // Background zones
                        RectangleMark(
                            xStart: .value("Start", minX),
                            xEnd: .value("End", maxX),
                            yStart: .value("Zone Start", minYAxisValue),
                            yEnd: .value("Zone End", optimalDurationThreshold)
                        )
                        .foregroundStyle(.orange.opacity(0.15))
                        .zIndex(0)

                        RectangleMark(
                            xStart: .value("Start", minX),
                            xEnd: .value("End", maxX),
                            yStart: .value("Zone Start", optimalDurationThreshold),
                            yEnd: .value("Zone End", maxYAxisValue)
                        )
                        .foregroundStyle(.green.opacity(0.15))
                        .zIndex(0)

                        // Optimal threshold line
                        RuleMark(y: .value("Optimal Threshold", optimalDurationThreshold))
                            .foregroundStyle(.green.opacity(0.6))
                            .lineStyle(StrokeStyle(lineWidth: 2, dash: [5, 5]))
                            .zIndex(1)

                        // Main line connecting all points
                        LineMark(
                            x: .value("Exhalation", \.exhalationNumber),
                            y: .value("Duration", \.duration)
                        )
                        .foregroundStyle(.white.opacity(0.8))
                        .lineStyle(StrokeStyle(lineWidth: 2, lineCap: .round, lineJoin: .round))
                        .zIndex(2)

                        // Data points
                        ForEach(exhalationData, id: \.exhalationNumber) { point in
                            PointMark(
                                x: .value("Exhalation", point.exhalationNumber),
                                y: .value("Duration", point.duration)
                            )
                            .foregroundStyle(point.isOptimal ? .green : .orange)
                            .symbolSize(64)
                            .zIndex(3)
                        }
                    }
                }
                .chartXAxis {
                    AxisMarks(position: .bottom, values: Array(1...max(1, exhalationData.count))) { value in
                        AxisGridLine()
                        AxisTick()
                        AxisValueLabel {
                            Text("\(value.as(Int.self) ?? 0)")
                                .foregroundColor(.white.opacity(0.7))
                                .font(.caption)
                        }
                    }
                }
                .chartYAxis {
                    AxisMarks(values: .stride(by: 1.0)) { value in
                        AxisGridLine()
                        AxisTick()
                        AxisValueLabel {
                            Text("\(String(format: "%.0f", value.as(Double.self) ?? 0))s")
                                .foregroundColor(.white.opacity(0.7))
                                .font(.caption)
                        }
                    }
                }
                .chartXScale(domain: 1...max(1, exhalationData.count))
                .chartYScale(domain: minYAxisValue...maxYAxisValue)
                .frame(height: 240)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.black.opacity(0.2))
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(Color.white.opacity(0.1), lineWidth: 1)
                        )
                )
                .padding(.horizontal, 4)

                Text("Exhalation Number")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                    .padding(.top, 4)
            }
            
            // Summary metrics
            exhalationSummary
        }
    }