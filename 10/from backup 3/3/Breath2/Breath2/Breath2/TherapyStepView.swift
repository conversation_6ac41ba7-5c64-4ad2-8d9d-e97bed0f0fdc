//
//  TherapyStepView.swift
//  Breath2
//
//  Created by <PERSON> on 02/06/2025.
//

import SwiftUI

struct TherapyStepView: View {
    let currentStep: Int
    let totalSteps: Int
    let stepPerformances: [Int: StepPerformance]

    init(currentStep: Int, totalSteps: Int, stepPerformances: [Int: StepPerformance] = [:]) {
        self.currentStep = currentStep
        self.totalSteps = totalSteps
        self.stepPerformances = stepPerformances
    }

    var body: some View {
        // Use adaptive grid layout for better scalability
        AdaptiveStepGridView(
            currentStep: currentStep,
            totalSteps: totalSteps,
            stepPerformances: stepPerformances
        )
    }
}

// MARK: - Adaptive Step Grid View

struct AdaptiveStepGridView: View {
    let currentStep: Int
    let totalSteps: Int
    let stepPerformances: [Int: StepPerformance]

    private var columns: [GridItem] {
        let maxColumns = min(totalSteps, 10)
        return Array(repeating: GridItem(.flexible(), spacing: 6), count: maxColumns)
    }

    private var blockSize: CGFloat {
        // Adaptive sizing based on number of steps
        switch totalSteps {
        case 1...10: return 32
        case 11...20: return 28
        case 21...30: return 24
        default: return 20
        }
    }

    var body: some View {
        LazyVGrid(columns: columns, spacing: 8) {
            ForEach(1...totalSteps, id: \.self) { step in
                stepBlock(for: step)
            }
        }
        .padding(.horizontal, 16)
    }

    private func stepBlock(for step: Int) -> some View {
        let performance = stepPerformances[step] ?? .notStarted

        return ZStack {
            RoundedRectangle(cornerRadius: blockSize * 0.2)
                .fill(stepBackgroundColor(for: step))
                .frame(width: blockSize, height: blockSize)
                .overlay(
                    RoundedRectangle(cornerRadius: blockSize * 0.2)
                        .stroke(stepBorderColor(for: step), lineWidth: 1.2)
                )

            if performance.isCompleted {
                // Completed step - checkmark
                Image(systemName: "checkmark")
                    .font(.system(size: blockSize * 0.35, weight: .bold))
                    .foregroundColor(.white)
            } else {
                // Step number
                Text("\(step)")
                    .font(.system(size: blockSize * 0.4, weight: step == currentStep ? .bold : .medium))
                    .foregroundColor(step == currentStep ? .white : (step < currentStep ? .white : .gray))
            }
        }
        .scaleEffect(step == currentStep && performance == .inProgress ? 1.08 : 1.0)
        .animation(
            step == currentStep && performance == .inProgress ?
                .easeInOut(duration: 1.0).repeatForever(autoreverses: true) :
                .easeInOut(duration: 0.3),
            value: performance
        )
    }

    private func stepBackgroundColor(for step: Int) -> Color {
        let performance = stepPerformances[step] ?? .notStarted

        if step == currentStep && performance == .inProgress {
            return .blue
        }

        switch performance {
        case .notStarted:
            return step < currentStep ? .gray.opacity(0.3) : .gray.opacity(0.2)
        case .inProgress:
            return .blue
        case .completedGreen:
            return .green
        case .completedAmber:
            return .orange
        case .completedRed:
            return .red
        }
    }

    private func stepBorderColor(for step: Int) -> Color {
        let performance = stepPerformances[step] ?? .notStarted

        if step == currentStep {
            return .blue.opacity(0.8)
        }

        switch performance {
        case .notStarted:
            return .gray.opacity(0.4)
        case .inProgress:
            return .blue.opacity(0.8)
        case .completedGreen:
            return .green.opacity(0.8)
        case .completedAmber:
            return .orange.opacity(0.8)
        case .completedRed:
            return .red.opacity(0.8)
        }
    }
}

// MARK: - Compact Step Blocks View

struct CompactStepBlocksView: View {
    let currentStep: Int
    let totalSteps: Int
    let stepPerformances: [Int: StepPerformance]

    private let itemsPerRow = 10
    private let blockSize: CGFloat = 28
    private let spacing: CGFloat = 6

    var body: some View {
        VStack(spacing: 8) {
            ForEach(0..<numberOfRows, id: \.self) { rowIndex in
                HStack(spacing: spacing) {
                    ForEach(stepsInRow(rowIndex), id: \.self) { step in
                        stepBlock(for: step)
                    }

                    // Add spacers to center the last row if it's not full
                    if rowIndex == numberOfRows - 1 && stepsInRow(rowIndex).count < itemsPerRow {
                        ForEach(0..<(itemsPerRow - stepsInRow(rowIndex).count), id: \.self) { _ in
                            Color.clear
                                .frame(width: blockSize, height: blockSize)
                        }
                    }
                }
                .frame(maxWidth: .infinity)
            }
        }
        .padding(.horizontal, 16)
    }

    private var numberOfRows: Int {
        (totalSteps + itemsPerRow - 1) / itemsPerRow
    }

    private func stepsInRow(_ rowIndex: Int) -> [Int] {
        let startStep = rowIndex * itemsPerRow + 1
        let endStep = min(startStep + itemsPerRow - 1, totalSteps)
        return Array(startStep...endStep)
    }

    private func stepBlock(for step: Int) -> some View {
        let performance = stepPerformances[step] ?? .notStarted

        return ZStack {
            RoundedRectangle(cornerRadius: 6)
                .fill(stepBackgroundColor(for: step))
                .frame(width: blockSize, height: blockSize)
                .overlay(
                    RoundedRectangle(cornerRadius: 6)
                        .stroke(stepBorderColor(for: step), lineWidth: 1.2)
                )

            if performance.isCompleted {
                // Completed step - checkmark
                Image(systemName: "checkmark")
                    .font(.system(size: 10, weight: .bold))
                    .foregroundColor(.white)
            } else {
                // Step number
                Text("\(step)")
                    .font(.system(size: 11, weight: step == currentStep ? .bold : .medium))
                    .foregroundColor(step == currentStep ? .white : (step < currentStep ? .white : .gray))
            }
        }
        .scaleEffect(step == currentStep && performance == .inProgress ? 1.08 : 1.0)
        .animation(
            step == currentStep && performance == .inProgress ?
                .easeInOut(duration: 1.0).repeatForever(autoreverses: true) :
                .easeInOut(duration: 0.3),
            value: performance
        )
    }

    private func stepBackgroundColor(for step: Int) -> Color {
        let performance = stepPerformances[step] ?? .notStarted

        if performance.isCompleted {
            return performance.color // Use performance-based color
        } else if step == currentStep {
            return .blue
        } else if step < currentStep {
            return .green.opacity(0.8)
        } else {
            return .gray.opacity(0.15) // Future step
        }
    }

    private func stepBorderColor(for step: Int) -> Color {
        let performance = stepPerformances[step] ?? .notStarted

        if performance.isCompleted {
            return performance.color // Use performance-based color
        } else if step == currentStep {
            return .blue
        } else if step < currentStep {
            return .green
        } else {
            return .gray.opacity(0.3)
        }
    }
}

// MARK: - Enhanced Step View with Labels

struct DetailedTherapyStepView: View {
    let currentStep: Int
    let totalSteps: Int
    let stepLabels: [String]
    
    init(currentStep: Int, totalSteps: Int, stepLabels: [String] = []) {
        self.currentStep = currentStep
        self.totalSteps = totalSteps
        self.stepLabels = stepLabels.isEmpty ? 
            Array(1...totalSteps).map { "Step \($0)" } : stepLabels
    }
    
    var body: some View {
        VStack(spacing: 16) {
            // Step indicators
            HStack(spacing: 0) {
                ForEach(1...totalSteps, id: \.self) { step in
                    HStack(spacing: 0) {
                        // Step circle
                        VStack(spacing: 8) {
                            ZStack {
                                Circle()
                                    .fill(stepBackgroundColor(for: step))
                                    .frame(width: 44, height: 44)
                                    .overlay(
                                        Circle()
                                            .stroke(stepBorderColor(for: step), lineWidth: 2)
                                    )
                                    .shadow(
                                        color: step == currentStep ? .blue.opacity(0.3) : .clear,
                                        radius: 8
                                    )
                                
                                if step < currentStep {
                                    // Completed step - checkmark
                                    Image(systemName: "checkmark")
                                        .font(.system(size: 18, weight: .bold))
                                        .foregroundColor(.white)
                                } else if step == currentStep {
                                    // Current step - step number with pulse
                                    Text("\(step)")
                                        .font(.system(size: 18, weight: .bold))
                                        .foregroundColor(.white)
                                } else {
                                    // Future step - step number
                                    Text("\(step)")
                                        .font(.system(size: 16, weight: .medium))
                                        .foregroundColor(.white.opacity(0.6))
                                }
                            }
                            
                            // Step label
                            if step <= stepLabels.count {
                                Text(stepLabels[step - 1])
                                    .font(.caption2)
                                    .foregroundColor(step <= currentStep ? .primary : .secondary)
                                    .fontWeight(step == currentStep ? .semibold : .regular)
                                    .multilineTextAlignment(.center)
                                    .frame(width: 60)
                            }
                        }
                        
                        // Connecting line (except for last step)
                        if step < totalSteps {
                            Rectangle()
                                .fill(lineColor(for: step))
                                .frame(height: 3)
                                .frame(maxWidth: .infinity)
                                .animation(.easeInOut(duration: 0.5), value: currentStep)
                        }
                    }
                }
            }
            .padding(.horizontal, 16)
            
            // Progress bar
            ProgressView(value: Double(currentStep - 1), total: Double(totalSteps - 1))
                .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                .scaleEffect(x: 1, y: 2, anchor: .center)
                .animation(.easeInOut(duration: 0.5), value: currentStep)
        }
    }
    
    private func stepBackgroundColor(for step: Int) -> Color {
        if step < currentStep {
            return .green // Completed
        } else if step == currentStep {
            return .blue // Current
        } else {
            return .gray.opacity(0.15) // Future
        }
    }
    
    private func stepBorderColor(for step: Int) -> Color {
        if step < currentStep {
            return .green
        } else if step == currentStep {
            return .blue
        } else {
            return .gray.opacity(0.3)
        }
    }
    
    private func lineColor(for step: Int) -> Color {
        if step < currentStep {
            return .green // Completed connection
        } else {
            return .gray.opacity(0.2) // Future connection
        }
    }
}

#Preview {
    VStack(spacing: 40) {
        VStack {
            Text("Simple Step View")
                .font(.headline)
            
            TherapyStepView(currentStep: 3, totalSteps: 6)
        }
        
        VStack {
            Text("Detailed Step View")
                .font(.headline)
            
            DetailedTherapyStepView(
                currentStep: 2,
                totalSteps: 6,
                stepLabels: [
                    "Prepare",
                    "Breathe In",
                    "Hold",
                    "Breathe Out",
                    "Rest",
                    "Complete"
                ]
            )
        }
    }
    .padding()
}
