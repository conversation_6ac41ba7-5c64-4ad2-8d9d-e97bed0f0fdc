//
//  SessionChartViews.swift
//  Breath2
//
//  Created by <PERSON> on 02/06/2025.
//

import SwiftUI
import Charts

// MARK: - Zone Performance Chart

struct ZonePerformanceChart: View {
    let session: CompletedSession
    
    private var zoneData: [ZoneDataPoint] {
        var dataPoints: [ZoneDataPoint] = []
        
        for (index, reading) in session.pressureReadings.enumerated() {
            let timeOffset = Double(index) * 0.2 // Assuming 5 readings per second
            let zone = pressureZone(for: reading.pressure)
            
            dataPoints.append(ZoneDataPoint(
                time: timeOffset,
                zone: zone,
                pressure: reading.pressure,
                step: reading.step
            ))
        }
        
        return dataPoints
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Zone legend
            HStack(spacing: 16) {
                ZoneLegendItem(color: .green, label: "In Zone", range: "Optimal effort")
                ZoneLegendItem(color: .orange, label: "Below Zone", range: "Needs more effort")
                ZoneLegendItem(color: .red, label: "Above Zone", range: "Too much effort")
            }
            .font(.caption)
            
            // Chart
            Chart(zoneData, id: \.time) { dataPoint in
                LineMark(
                    x: .value("Time", dataPoint.time),
                    y: .value("Zone", dataPoint.zoneValue)
                )
                .foregroundStyle(dataPoint.zone.color)
                .lineStyle(StrokeStyle(lineWidth: 3))
                
                // Add breath markers
                if dataPoint.isBreathStart {
                    PointMark(
                        x: .value("Time", dataPoint.time),
                        y: .value("Zone", dataPoint.zoneValue)
                    )
                    .foregroundStyle(.white)
                    .symbol(.circle)
                    .symbolSize(40)
                }
            }
            .frame(height: 200)
            .chartXAxis {
                AxisMarks(values: .automatic(desiredCount: 6)) { value in
                    AxisGridLine()
                    AxisTick()
                    AxisValueLabel(centered: true) {
                        if let time = value.as(Double.self) {
                            Text("\(Int(time))s")
                                .foregroundColor(.white.opacity(0.7))
                                .font(.caption)
                        }
                    }
                }
            }
            .chartYAxis {
                AxisMarks(values: [1, 2, 3]) { value in
                    AxisGridLine()
                    AxisTick()
                    AxisValueLabel {
                        if let zoneValue = value.as(Double.self) {
                            let zoneName = zoneValue == 1 ? "Above" : zoneValue == 2 ? "Below" : "In Zone"
                            Text(zoneName)
                                .foregroundColor(.white.opacity(0.7))
                                .font(.caption)
                        }
                    }
                }
            }
            .chartBackground { chartProxy in
                ZStack {
                    // Zone background colors
                    Rectangle()
                        .fill(.green.opacity(0.1))
                        .clipShape(Rectangle())
                }
            }
            
            // Zone performance summary
            zonePerformanceSummary
        }
    }
    
    private var zonePerformanceSummary: some View {
        HStack(spacing: 16) {
            ZonePerformanceCard(
                zone: "In Zone",
                percentage: greenZonePercentage,
                color: .green
            )

            ZonePerformanceCard(
                zone: "Below Zone",
                percentage: amberZonePercentage,
                color: .orange
            )

            ZonePerformanceCard(
                zone: "Above Zone",
                percentage: redZonePercentage,
                color: .red
            )
        }
    }
    
    private func pressureZone(for pressure: Double) -> PressureZone {
        if pressure >= 10.0 && pressure <= 20.0 {
            return .green
        } else if pressure > 20.0 {
            return .red
        } else {
            return .amber
        }
    }
    
    private var greenZonePercentage: Double {
        let greenReadings = session.pressureReadings.filter { reading in
            reading.pressure >= 10.0 && reading.pressure <= 20.0
        }
        return Double(greenReadings.count) / Double(session.pressureReadings.count) * 100
    }
    
    private var amberZonePercentage: Double {
        let amberReadings = session.pressureReadings.filter { reading in
            reading.pressure < 10.0 && reading.pressure > 0
        }
        return Double(amberReadings.count) / Double(session.pressureReadings.count) * 100
    }

    private var redZonePercentage: Double {
        let redReadings = session.pressureReadings.filter { reading in
            reading.pressure > 20.0
        }
        return Double(redReadings.count) / Double(session.pressureReadings.count) * 100
    }
}

// MARK: - Pressure Timeline Chart

struct PressureTimelineChart: View {
    let session: CompletedSession

    // MARK: - Zone Constants (matching existing codebase standards)
    private let belowZoneThreshold: Double = 10.0  // < 10.0 cm H₂O
    private let aboveZoneThreshold: Double = 20.0  // > 20.0 cm H₂O
    // In Zone: 10.0-20.0 cm H₂O

    private var exhalationData: [ExhalationPressureDataPoint] {
        // Group pressure readings by step (exhalation number) and calculate average pressure
        let groupedReadings = Dictionary(grouping: session.pressureReadings) { $0.step }

        // Only create data points for exhalations that have actual pressure data
        // This will create natural gaps in the line for missing data
        return groupedReadings.compactMap { (step, readings) in
            guard !readings.isEmpty else { return nil }

            let averagePressure = readings.map { $0.pressure }.reduce(0, +) / Double(readings.count)
            let zone = pressureZone(for: averagePressure)

            return ExhalationPressureDataPoint(
                exhalationNumber: step,
                averagePressure: averagePressure,
                zone: zone
            )
        }.sorted { $0.exhalationNumber < $1.exhalationNumber }
    }

    private var maxPressure: Double {
        return session.pressureReadings.map { $0.pressure }.max() ?? 25.0
    }

    private var minPressure: Double {
        return session.pressureReadings.map { $0.pressure }.min() ?? 0.0
    }

    private var chartLegend: some View {
        HStack(spacing: 20) {
            CompactLegendItem(
                color: .green,
                label: "In Zone",
                range: "10-20 cm H₂O",
                isOptimal: true
            )
            CompactLegendItem(
                color: .orange,
                label: "Below Zone",
                range: "<10 cm H₂O",
                isOptimal: false
            )
            CompactLegendItem(
                color: .red,
                label: "Above Zone",
                range: ">20 cm H₂O",
                isOptimal: false
            )
        }
        .padding(.horizontal, 4) // Match container alignment
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
            // Chart legend
            chartLegend

            // Chart with X-axis label - properly aligned container
            VStack(spacing: 12) {
                // Elegant chart with proper contrast and visual appeal
                modernChart
                    .frame(height: 240)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.black.opacity(0.2))
                            .overlay(
                                RoundedRectangle(cornerRadius: 16)
                                    .stroke(Color.white.opacity(0.1), lineWidth: 1)
                            )
                    )

                // X-axis label
                Text("Exhalation Number")
                    .font(.system(size: 12, weight: .medium, design: .rounded))
                    .foregroundColor(.white.opacity(0.7))
                    .frame(maxWidth: .infinity, alignment: .center)

                // Missing data indicator
                if !exhalationData.isEmpty {
                    let maxDataExhalation = exhalationData.map { $0.exhalationNumber }.max() ?? 0
                    if maxDataExhalation < session.stepsCompleted {
                        Text("Data available for exhalations 1-\(maxDataExhalation) only")
                            .font(.system(size: 10, weight: .medium, design: .rounded))
                            .foregroundColor(.white.opacity(0.5))
                            .frame(maxWidth: .infinity, alignment: .center)
                            .padding(.top, 4)
                    }
                }
            }

            // Zone performance summary - aligned with chart
            zonePerformanceSummary
        }
    }

    private var modernZoneLegend: some View {
        HStack(spacing: 20) {
            ModernZoneLegendItem(
                color: .green,
                label: "In Zone",
                range: "10-20 cm H₂O",
                isOptimal: true
            )
            ModernZoneLegendItem(
                color: .orange,
                label: "Below Zone",
                range: "<10 cm H₂O",
                isOptimal: false
            )
            ModernZoneLegendItem(
                color: .red,
                label: "Above Zone",
                range: ">20 cm H₂O",
                isOptimal: false
            )
        }
        .padding(.horizontal, 8)
    }

    private var modernChart: some View {
        Chart {
            // BACKGROUND ZONES - Show across the full range of steps
            RectangleMark(
                xStart: .value("Start", 1),
                xEnd: .value("End", session.totalSteps),
                yStart: .value("Below Start", 0),
                yEnd: .value("Below End", belowZoneThreshold)
            )
            .foregroundStyle(.orange.opacity(0.15))
            .zIndex(0)

            RectangleMark(
                xStart: .value("Start", 1),
                xEnd: .value("End", session.totalSteps),
                yStart: .value("In Zone Start", belowZoneThreshold),
                yEnd: .value("In Zone End", aboveZoneThreshold)
            )
            .foregroundStyle(.green.opacity(0.15))
            .zIndex(0)

            RectangleMark(
                xStart: .value("Start", 1),
                xEnd: .value("End", session.totalSteps),
                yStart: .value("Above Start", aboveZoneThreshold),
                yEnd: .value("Above End", 40)
            )
            .foregroundStyle(.red.opacity(0.15))
            .zIndex(0)

            // PRESSURE LINE - Draw on top with zone-based colors
            ForEach(Array(exhalationData.enumerated()), id: \.offset) { index, dataPoint in
                LineMark(
                    x: .value("Exhalation", dataPoint.exhalationNumber),
                    y: .value("Average Pressure", dataPoint.averagePressure)
                )
                .foregroundStyle(colorForPressure(dataPoint.averagePressure))
                .lineStyle(StrokeStyle(lineWidth: 3, lineCap: .round, lineJoin: .round))
                .zIndex(2)

                // Add data points for all exhalations
                PointMark(
                    x: .value("Exhalation", dataPoint.exhalationNumber),
                    y: .value("Average Pressure", dataPoint.averagePressure)
                )
                .foregroundStyle(colorForPressure(dataPoint.averagePressure))
                .symbolSize(20)
                .zIndex(3)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .chartYScale(domain: 0...40) // Fixed Y-axis range
        .chartXScale(domain: 1...max(1, session.totalSteps))
        .chartXAxis {
            AxisMarks(values: (1...session.totalSteps).map { Double($0) }) { value in
                AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5))
                    .foregroundStyle(.white.opacity(0.2))
                AxisTick(stroke: StrokeStyle(lineWidth: 0.5))
                    .foregroundStyle(.white.opacity(0.3))
                AxisValueLabel(centered: true) {
                    if let exhalationNum = value.as(Double.self) {
                        Text("\(Int(exhalationNum))")
                            .foregroundColor(.white.opacity(0.8))
                            .font(.system(size: 11, weight: .medium, design: .rounded))
                    }
                }
            }
        }
        .chartYAxis {
            AxisMarks(values: [0, 10, 20, 30, 40]) { value in
                AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5, dash: [2, 4]))
                    .foregroundStyle(.white.opacity(0.15))
                AxisTick(stroke: StrokeStyle(lineWidth: 0.5))
                    .foregroundStyle(.white.opacity(0.3))
                // Y-axis numbers hidden - no AxisValueLabel
            }
        }
    }
    
    private var zonePerformanceSummary: some View {
        HStack(spacing: 16) {
            ZonePerformanceCard(
                zone: "In Zone",
                percentage: greenZonePercentage,
                color: .green
            )

            ZonePerformanceCard(
                zone: "Below Zone",
                percentage: amberZonePercentage,
                color: .orange
            )

            ZonePerformanceCard(
                zone: "Above Zone",
                percentage: redZonePercentage,
                color: .red
            )
        }
        .padding(.horizontal, 4) // Match chart container padding
    }

    private func pressureZone(for pressure: Double) -> PressureZone {
        if pressure >= belowZoneThreshold && pressure <= aboveZoneThreshold {
            return .green
        } else if pressure > aboveZoneThreshold {
            return .red
        } else {
            return .amber
        }
    }

    private func colorForPressure(_ pressure: Double) -> Color {
        if pressure >= belowZoneThreshold && pressure <= aboveZoneThreshold {
            return .green
        } else if pressure > aboveZoneThreshold {
            return .red
        } else {
            return .orange
        }
    }

    private func zoneNameForPressure(_ pressure: Double) -> String {
        if pressure < belowZoneThreshold {
            return "Below"
        } else if pressure <= aboveZoneThreshold {
            return "In Zone"
        } else {
            return "Above"
        }
    }

    private var greenZonePercentage: Double {
        let greenExhalations = exhalationData.filter { dataPoint in
            dataPoint.averagePressure >= belowZoneThreshold && dataPoint.averagePressure <= aboveZoneThreshold
        }
        guard !exhalationData.isEmpty else { return 0.0 }
        return Double(greenExhalations.count) / Double(exhalationData.count) * 100
    }

    private var amberZonePercentage: Double {
        let amberExhalations = exhalationData.filter { dataPoint in
            dataPoint.averagePressure < belowZoneThreshold && dataPoint.averagePressure > 0
        }
        guard !exhalationData.isEmpty else { return 0.0 }
        return Double(amberExhalations.count) / Double(exhalationData.count) * 100
    }

    private var redZonePercentage: Double {
        let redExhalations = exhalationData.filter { dataPoint in
            dataPoint.averagePressure > aboveZoneThreshold
        }
        guard !exhalationData.isEmpty else { return 0.0 }
        return Double(redExhalations.count) / Double(exhalationData.count) * 100
    }


}

// MARK: - Supporting Data Models

struct ZoneDataPoint {
    let time: Double
    let zone: PressureZone
    let pressure: Double
    let step: Int
    
    var zoneValue: Double {
        switch zone {
        case .red: return 1
        case .amber: return 2
        case .green: return 3
        }
    }
    
    var isBreathStart: Bool {
        // Simplified logic - in real implementation, this would be based on breath detection
        return Int(time) % 4 == 0 && time > 0
    }
}

struct PressureDataPoint {
    let time: Double
    let pressure: Double
    let step: Int
}

struct PressureZoneDataPoint {
    let time: Double
    let pressure: Double
    let step: Int
    let zone: PressureZone

    var zoneValue: Double {
        switch zone {
        case .red: return 1
        case .amber: return 2
        case .green: return 3
        }
    }
}

struct ExhalationPressureDataPoint {
    let exhalationNumber: Int
    let averagePressure: Double
    let zone: PressureZone
}



enum PressureZone {
    case green, amber, red
    
    var color: Color {
        switch self {
        case .green: return .green
        case .amber: return .orange
        case .red: return .red
        }
    }
}

// MARK: - Supporting Views

struct ZoneLegendItem: View {
    let color: Color
    let label: String
    let range: String

    var body: some View {
        HStack(spacing: 6) {
            Circle()
                .fill(color)
                .frame(width: 8, height: 8)

            VStack(alignment: .leading, spacing: 2) {
                Text(label)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white)

                Text(range)
                    .font(.caption2)
                    .foregroundColor(.white.opacity(0.7))
            }
        }
    }
}

struct CompactLegendItem: View {
    let color: Color
    let label: String
    let range: String
    let isOptimal: Bool

    var body: some View {
        HStack(spacing: 8) {
            // Color indicator
            Circle()
                .fill(color)
                .frame(width: 8, height: 8)
                .shadow(color: color.opacity(0.6), radius: isOptimal ? 2 : 1)

            VStack(alignment: .leading, spacing: 2) {
                HStack(spacing: 4) {
                    Text(label)
                        .font(.system(size: 12, weight: .semibold, design: .rounded))
                        .foregroundColor(.white)

                    if isOptimal {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 8))
                            .foregroundColor(.green)
                    }
                }

                Text(range)
                    .font(.system(size: 10, weight: .medium, design: .rounded))
                    .foregroundColor(.white.opacity(0.7))
            }
        }
    }
}

struct ModernZoneLegendItem: View {
    let color: Color
    let label: String
    let range: String
    let isOptimal: Bool

    var body: some View {
        HStack(spacing: 10) {
            // Modern indicator with subtle glow effect
            ZStack {
                Circle()
                    .fill(color.opacity(0.2))
                    .frame(width: 16, height: 16)

                Circle()
                    .fill(color)
                    .frame(width: 8, height: 8)
                    .shadow(color: color.opacity(0.6), radius: isOptimal ? 3 : 1)
            }

            VStack(alignment: .leading, spacing: 3) {
                HStack(spacing: 4) {
                    Text(label)
                        .font(.system(size: 13, weight: .semibold, design: .rounded))
                        .foregroundColor(.white)

                    if isOptimal {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 10))
                            .foregroundColor(.green)
                    }
                }

                Text(range)
                    .font(.system(size: 11, weight: .medium, design: .rounded))
                    .foregroundColor(.white.opacity(0.7))
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(color.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

struct ZonePerformanceCard: View {
    let zone: String
    let percentage: Double
    let color: Color

    var body: some View {
        VStack(spacing: 6) {
            Text("\(Int(percentage))%")
                .font(.system(size: 18, weight: .bold, design: .rounded))
                .foregroundColor(color)

            Text(zone)
                .font(.caption)
                .foregroundColor(.white.opacity(0.7))
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(color.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

struct StatisticCard: View {
    let title: String
    let value: String
    let unit: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Text(title)
                .font(.caption)
                .foregroundColor(.white.opacity(0.7))
            
            HStack(alignment: .bottom, spacing: 2) {
                Text(value)
                    .font(.system(size: 16, weight: .bold, design: .rounded))
                    .foregroundColor(color)
                
                Text(unit)
                    .font(.caption2)
                    .foregroundColor(.white.opacity(0.7))
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(Color.white.opacity(0.05))
        )
    }
}
