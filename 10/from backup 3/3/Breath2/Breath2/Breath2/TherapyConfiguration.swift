//
//  TherapyConfiguration.swift
//  Breath2
//
//  Created by <PERSON> on 02/06/2025.
//

import Foundation
import SwiftUI

// MARK: - Therapy Configuration

class TherapyConfiguration: ObservableObject, Codable {
    
    // MARK: - Published Properties
    
    @Published var dailyBlocks: [TherapyBlock] = []
    @Published var currentBlockIndex: Int = 0
    @Published var sessionsCompletedToday: Int = 0
    @Published var lastSessionDate: Date?
    
    // MARK: - Configuration Limits
    
    static let maxExhalationsPerDay = 30
    static let maxSessionsPerDay = 3
    static let maxExhalationsPerBlock = 15
    static let minExhalationsPerBlock = 5
    
    // MARK: - Initialization
    
    init() {
        setupDefaultConfiguration()
    }
    
    // MARK: - Default Configurations
    
    private func setupDefaultConfiguration() {
        // Default: 3 blocks of 10 exhalations each
        dailyBlocks = [
            TherapyBlock(id: 1, exhalations: 10, name: "Morning Session"),
            TherapyBlock(id: 2, exhalations: 10, name: "Afternoon Session"),
            TherapyBlock(id: 3, exhalations: 10, name: "Evening Session")
        ]
    }
    
    // MARK: - Preset Configurations
    
    static func singleSession() -> TherapyConfiguration {
        let config = TherapyConfiguration()
        config.dailyBlocks = [
            TherapyBlock(id: 1, exhalations: 10, name: "Single Session")
        ]
        return config
    }
    
    static func twoSessions() -> TherapyConfiguration {
        let config = TherapyConfiguration()
        config.dailyBlocks = [
            TherapyBlock(id: 1, exhalations: 15, name: "Morning Session"),
            TherapyBlock(id: 2, exhalations: 15, name: "Evening Session")
        ]
        return config
    }
    
    static func threeSessions() -> TherapyConfiguration {
        let config = TherapyConfiguration()
        config.dailyBlocks = [
            TherapyBlock(id: 1, exhalations: 10, name: "Morning Session"),
            TherapyBlock(id: 2, exhalations: 10, name: "Afternoon Session"),
            TherapyBlock(id: 3, exhalations: 10, name: "Evening Session")
        ]
        return config
    }
    
    // MARK: - Session Management
    
    var currentBlock: TherapyBlock? {
        guard currentBlockIndex < dailyBlocks.count else { return nil }
        return dailyBlocks[currentBlockIndex]
    }
    
    var hasMoreSessions: Bool {
        return currentBlockIndex < dailyBlocks.count
    }
    
    var totalExhalationsToday: Int {
        return dailyBlocks.reduce(0) { $0 + $1.exhalations }
    }
    
    var completedExhalationsToday: Int {
        let completedBlocks = dailyBlocks.prefix(currentBlockIndex)
        return completedBlocks.reduce(0) { $0 + $1.exhalations }
    }
    
    var progressToday: Double {
        guard totalExhalationsToday > 0 else { return 0 }
        return Double(completedExhalationsToday) / Double(totalExhalationsToday)
    }
    
    func completeCurrentSession() {
        if currentBlockIndex < dailyBlocks.count {
            dailyBlocks[currentBlockIndex].isCompleted = true
            currentBlockIndex += 1
            sessionsCompletedToday += 1
            lastSessionDate = Date()
        }
    }
    
    func resetDailyProgress() {
        currentBlockIndex = 0
        sessionsCompletedToday = 0
        for i in 0..<dailyBlocks.count {
            dailyBlocks[i].isCompleted = false
        }
    }
    
    // MARK: - Validation
    
    func validateConfiguration() -> Bool {
        let totalExhalations = totalExhalationsToday
        return totalExhalations <= Self.maxExhalationsPerDay &&
               dailyBlocks.count <= Self.maxSessionsPerDay &&
               dailyBlocks.allSatisfy { block in
                   block.exhalations >= Self.minExhalationsPerBlock &&
                   block.exhalations <= Self.maxExhalationsPerBlock
               }
    }
    
    // MARK: - Codable Implementation
    
    enum CodingKeys: String, CodingKey {
        case dailyBlocks, currentBlockIndex, sessionsCompletedToday, lastSessionDate
    }
    
    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        dailyBlocks = try container.decode([TherapyBlock].self, forKey: .dailyBlocks)
        currentBlockIndex = try container.decode(Int.self, forKey: .currentBlockIndex)
        sessionsCompletedToday = try container.decode(Int.self, forKey: .sessionsCompletedToday)
        lastSessionDate = try container.decodeIfPresent(Date.self, forKey: .lastSessionDate)
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(dailyBlocks, forKey: .dailyBlocks)
        try container.encode(currentBlockIndex, forKey: .currentBlockIndex)
        try container.encode(sessionsCompletedToday, forKey: .sessionsCompletedToday)
        try container.encodeIfPresent(lastSessionDate, forKey: .lastSessionDate)
    }
}

// MARK: - Therapy Block

struct TherapyBlock: Identifiable, Codable, Equatable {
    let id: Int
    var exhalations: Int
    var name: String
    var isCompleted: Bool = false
    var completedAt: Date?
    
    var estimatedDuration: TimeInterval {
        // Estimate ~4 seconds per exhalation + setup time
        return Double(exhalations) * 4.0 + 30.0
    }
    
    var formattedDuration: String {
        let minutes = Int(estimatedDuration) / 60
        let seconds = Int(estimatedDuration) % 60
        return "\(minutes)m \(seconds)s"
    }
}

// MARK: - Daily Reset Helper

extension TherapyConfiguration {
    
    func checkAndResetIfNewDay() {
        guard let lastDate = lastSessionDate else { return }
        
        let calendar = Calendar.current
        if !calendar.isDate(lastDate, inSameDayAs: Date()) {
            resetDailyProgress()
        }
    }
}
