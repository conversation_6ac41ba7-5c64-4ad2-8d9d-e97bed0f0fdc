//
//  ExhalationTimeChart.swift
//  Breath2
//
//  Created by <PERSON> on 02/06/2025.
//

import SwiftUI
import Charts

// MARK: - Exhalation Time Chart

struct ExhalationTimeChart: View {
    let session: CompletedSession
    
    private var exhalationData: [ExhalationDataPoint] {
        let durations = session.exhalationDurations
        return durations.enumerated().map { index, duration in
            ExhalationDataPoint(
                exhalationNumber: index + 1,
                duration: duration,
                isOptimal: duration >= 3.0
            )
        }
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Chart title and legend
            VStack(alignment: .leading, spacing: 8) {
                Text("Exhalation Duration Compliance")
                    .font(.title3)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                // Legend
                HStack(spacing: 16) {
                    LegendItem(color: .green, label: "≥3.0s (Optimal)")
                    LegendItem(color: .orange, label: "<3.0s (Too Short)")
                }
                .font(.caption)
            }
            
            // Chart
            Chart(exhalationData, id: \.exhalationNumber) { dataPoint in
                BarMark(
                    x: .value("Exhalation", dataPoint.exhalationNumber),
                    y: .value("Duration", dataPoint.duration)
                )
                .foregroundStyle(dataPoint.isOptimal ? .green : .orange)
                .cornerRadius(4)
            }
            .frame(height: 200)
            .chartXAxis {
                AxisMarks(values: .automatic(desiredCount: min(session.stepsCompleted, 10))) { value in
                    AxisGridLine()
                    AxisTick()
                    AxisValueLabel {
                        if let exhalationNum = value.as(Int.self) {
                            Text("\(exhalationNum)")
                                .foregroundColor(.gray)
                                .font(.caption)
                        }
                    }
                }
            }
            .chartYAxis {
                AxisMarks(values: .automatic(desiredCount: 6)) { value in
                    AxisGridLine()
                    AxisTick()
                    AxisValueLabel {
                        if let duration = value.as(Double.self) {
                            Text("\(String(format: "%.1f", duration))s")
                                .foregroundColor(.gray)
                                .font(.caption)
                        }
                    }
                }
            }
            .chartBackground { chartProxy in
                ZStack {
                    // Target duration line at 3.0 seconds
                    Rectangle()
                        .fill(.green.opacity(0.1))
                        .frame(height: chartProxy.plotContainerFrame.height)
                        .clipShape(Rectangle())
                }
            }
            
            // Summary metrics
            exhalationSummary
        }
    }
    
    private var exhalationSummary: some View {
        HStack(spacing: 16) {
            ExhalationMetricCard(
                title: "Optimal Duration",
                value: "\(optimalExhalationsCount)/\(session.stepsCompleted)",
                subtitle: "\(optimalPercentage)%",
                color: .green
            )
            
            ExhalationMetricCard(
                title: "Average Duration",
                value: "\(String(format: "%.1f", session.averageExhalationDuration))s",
                subtitle: "Target: 3.0s+",
                color: session.averageExhalationDuration >= 3.0 ? .green : .orange
            )
            
            ExhalationMetricCard(
                title: "Consistency",
                value: consistencyRating,
                subtitle: "Duration variance",
                color: consistencyColor
            )
        }
    }
    
    private var optimalExhalationsCount: Int {
        exhalationData.filter { $0.isOptimal }.count
    }
    
    private var optimalPercentage: Int {
        guard session.stepsCompleted > 0 else { return 0 }
        return Int(Double(optimalExhalationsCount) / Double(session.stepsCompleted) * 100)
    }
    
    private var consistencyRating: String {
        let durations = session.exhalationDurations
        guard durations.count > 1 else { return "N/A" }
        
        let average = durations.reduce(0, +) / Double(durations.count)
        let variance = durations.map { pow($0 - average, 2) }.reduce(0, +) / Double(durations.count)
        let standardDeviation = sqrt(variance)
        
        if standardDeviation < 0.5 {
            return "Excellent"
        } else if standardDeviation < 1.0 {
            return "Good"
        } else if standardDeviation < 1.5 {
            return "Fair"
        } else {
            return "Variable"
        }
    }
    
    private var consistencyColor: Color {
        switch consistencyRating {
        case "Excellent": return .green
        case "Good": return .blue
        case "Fair": return .orange
        default: return .red
        }
    }
}

// MARK: - Supporting Views

struct ExhalationDataPoint {
    let exhalationNumber: Int
    let duration: Double
    let isOptimal: Bool
}

struct LegendItem: View {
    let color: Color
    let label: String
    
    var body: some View {
        HStack(spacing: 4) {
            Circle()
                .fill(color)
                .frame(width: 8, height: 8)
            Text(label)
                .foregroundColor(.gray)
        }
    }
}

struct ExhalationMetricCard: View {
    let title: String
    let value: String
    let subtitle: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Text(value)
                .font(.system(size: 16, weight: .bold, design: .rounded))
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.white)
            
            Text(subtitle)
                .font(.caption2)
                .foregroundColor(.gray)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(color.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

#Preview {
    // Create sample session data for preview with deterministic values
    let sampleReadings = (1...50).map { i in
        let pressure = 15.0 + sin(Double(i) * 0.3) * 5.0 // Deterministic variation
        return StoredPressureReading(
            timestamp: Date().addingTimeInterval(Double(i)),
            pressure: max(8.0, min(25.0, pressure)),
            step: i / 5 + 1
        )
    }

    let sampleSession = CompletedSession(
        date: Date(),
        duration: 180,
        quality: .good,
        stepsCompleted: 10,
        totalSteps: 10,
        averagePressure: 15.0,
        maxPressure: 22.0,
        minPressure: 8.0,
        pressureReadings: sampleReadings,
        feedback: "Good session!"
    )

    ExhalationTimeChart(session: sampleSession)
        .padding()
        .background(Color.black)
}
