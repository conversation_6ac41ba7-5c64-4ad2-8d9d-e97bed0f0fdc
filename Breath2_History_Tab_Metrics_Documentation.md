# 📊 Breath2 History Tab - Metrics & Data Analysis Documentation

## 🎯 Overview

This document provides a comprehensive analysis of all metrics, calculations, and data structures used in the Breath2 app's History Tab and Session Detail views. The system transforms raw pressure sensor data into meaningful therapeutic insights while maintaining a clean, intuitive interface focused on treatment adherence and compliance.

---

## 📋 Table of Contents

1. [Raw Data Sources](#-raw-data-sources)
2. [Therapeutic Zone System](#-therapeutic-zone-system)
3. [Core Computed Metrics](#-core-computed-metrics)
4. [Adherence Calculations](#-adherence-calculations)
5. [Session Quality System](#-session-quality-system)
6. [Chart Data Structures](#-chart-data-structures)
7. [Time-Based Filtering](#-time-based-filtering)
8. [Information Hierarchy](#-information-hierarchy)

---

## 🗄️ Raw Data Sources

### Primary Data Models

#### `CompletedSession`
**Purpose**: Core session data structure containing all session information
```swift
struct CompletedSession: Codable, Identifiable {
    let id: UUID
    let date: Date
    let duration: TimeInterval
    let quality: SessionQuality
    let stepsCompleted: Int
    let totalSteps: Int
    let averagePressure: Double
    let maxPressure: Double
    let minPressure: Double
    let pressureReadings: [StoredPressureReading]
    let feedback: String
}
```

#### `StoredPressureReading`
**Purpose**: Individual pressure measurements with timing data
```swift
struct StoredPressureReading: Codable {
    let timestamp: Date
    let pressure: Double  // cm H₂O
    let step: Int         // Exhalation number
}
```

#### `TherapyConfiguration`
**Purpose**: Treatment plan and target settings
- Daily blocks configuration
- Planned sessions per timeframe
- Target exhalation counts

---

## 🎯 Therapeutic Zone System

### Zone Definitions
The app uses a three-zone therapeutic system instead of displaying raw pressure values:

| Zone | Pressure Range | Display Name | Description | Color |
|------|----------------|--------------|-------------|-------|
| **Green** | 10.0-20.0 cm H₂O | "In Zone" | Optimal exhalation effort | 🟢 Green |
| **Amber** | < 10.0 cm H₂O | "Below Zone" | Needs more effort | 🟠 Orange |
| **Red** | > 20.0 cm H₂O | "Above Zone" | Too much effort | 🔴 Red |

### Zone Classification Logic
```swift
func pressureZone(for pressure: Double) -> ExhalationEffortZone {
    if pressure >= 10.0 && pressure <= 20.0 {
        return .green  // In Zone
    } else if pressure > 20.0 {
        return .red    // Above Zone
    } else {
        return .amber  // Below Zone
    }
}
```

---

## 📊 Core Computed Metrics

### 1. Green Zone Percentage
**Purpose**: Percentage of time spent in optimal therapeutic zone
**Calculation**:
```swift
var greenZonePercentage: Double {
    guard !pressureReadings.isEmpty else { return 0.0 }
    let greenReadings = pressureReadings.filter { reading in
        reading.pressure >= 10.0 && reading.pressure <= 20.0
    }
    return Double(greenReadings.count) / Double(pressureReadings.count) * 100
}
```
**Display**: Used in session headers and effort compliance views
**Target**: ≥80% for optimal therapy

### 2. Average Exhalation Duration
**Purpose**: Mean duration per exhalation breath
**Calculation**:
```swift
var averageExhalationDuration: Double {
    guard stepsCompleted > 0 else { return 0.0 }
    return duration / Double(stepsCompleted)
}
```
**Display**: Time compliance sections
**Target**: ≥3.0 seconds per exhalation

### 3. Predominant Effort Zone
**Purpose**: Most common therapeutic zone during session
**Calculation**:
```swift
var predominantEffortZone: ExhalationEffortZone {
    guard !pressureReadings.isEmpty else { return .amber }
    
    let greenCount = pressureReadings.filter { $0.pressure >= 10.0 && $0.pressure <= 20.0 }.count
    let redCount = pressureReadings.filter { $0.pressure > 20.0 }.count
    let amberCount = pressureReadings.count - greenCount - redCount
    
    let maxCount = max(greenCount, max(amberCount, redCount))
    
    if maxCount == greenCount { return .green }
    else if maxCount == redCount { return .red }
    else { return .amber }
}
```
**Display**: Effort overview cards
**Optimal**: Green zone predominance

### 4. Zone Consistency Score
**Purpose**: 0-100 score indicating adherence to target zones with penalty system
**Calculation**:
```swift
var zoneConsistencyScore: Double {
    guard !pressureReadings.isEmpty else { return 0.0 }
    
    // Primary metric: time in green zone
    let greenZoneTime = greenZonePercentage
    
    // Penalty for red zone usage
    let redReadings = pressureReadings.filter { $0.pressure > 20.0 }
    let redZonePercentage = Double(redReadings.count) / Double(pressureReadings.count) * 100
    let redZonePenalty = min(redZonePercentage * 0.5, 20.0) // Max 20 point penalty
    
    return max(0.0, min(100.0, greenZoneTime - redZonePenalty))
}
```
**Display**: Zone consistency cards
**Scoring**: 
- ≥80%: Excellent (Green)
- ≥60%: Good (Orange) 
- <60%: Needs Improvement (Red)

### 5. Exhalation Durations Array
**Purpose**: Individual breath duration data for time charts
**Calculation**:
```swift
var exhalationDurations: [Double] {
    guard stepsCompleted > 0 else { return [] }
    
    let avgDuration = averageExhalationDuration
    
    // Generate estimated durations with realistic variation
    return Array(1...stepsCompleted).map { _ in
        let variation = Double.random(in: -0.5...0.5)
        return max(1.0, avgDuration + variation)
    }
}
```
**Display**: Time tab bar charts
**Note**: Currently estimated; future versions will use actual breath timing

---

## 📈 Adherence Calculations

### Overall Adherence Score
**Purpose**: Primary adherence metric showing session completion vs. planned
**Calculation**:
```swift
private var adherenceScore: Double {
    guard plannedSessions > 0 else { return 0 }
    return min(Double(filteredSessions.count) / Double(plannedSessions), 1.0)
}
```

### Planned Sessions by Timeframe
**Calculation Logic**:
```swift
private var plannedSessions: Int {
    switch selectedTimeframe {
    case .day:   return therapyConfiguration.dailyBlocks.count
    case .week:  return therapyConfiguration.dailyBlocks.count * 7
    case .month: return therapyConfiguration.dailyBlocks.count * 30
    case .year:  return therapyConfiguration.dailyBlocks.count * 365
    }
}
```

### Color-Coded Performance Levels
| Score Range | Color | Description |
|-------------|-------|-------------|
| ≥80% | 🟢 Green | Excellent adherence |
| ≥60% | 🟡 Yellow | Good adherence |
| ≥40% | 🟠 Orange | Fair adherence |
| <40% | 🔴 Red | Poor adherence |

---

## ⭐ Session Quality System

### Quality Levels & Scoring
```swift
enum SessionQuality: Codable {
    case none, poor, fair, good, excellent
}
```

### Quality Calculation Logic
**Based on percentage of time in green zone**:
```swift
private func calculateSessionQuality() {
    let inTargetCount = pressureReadings.filter { reading in
        reading.pressure >= 10.0 && reading.pressure <= 20.0
    }.count
    
    let percentage = Double(inTargetCount) / Double(pressureReadings.count)
    
    if percentage >= 0.8      { sessionQuality = .excellent }  // ≥80%
    else if percentage >= 0.6 { sessionQuality = .good }       // ≥60%
    else if percentage >= 0.4 { sessionQuality = .fair }       // ≥40%
    else                      { sessionQuality = .poor }       // <40%
}
```

### Quality Scoring for Averages
```swift
switch session.quality {
case .excellent: return 1.0
case .good:      return 0.8
case .fair:      return 0.6
case .poor:      return 0.4
case .none:      return 0.0
}
```

### Visual Indicators
| Quality | Icon | Color | Description |
|---------|------|-------|-------------|
| Excellent | ⭐ star.fill | 🟢 Green | ≥80% in target zone |
| Good | ✅ checkmark.circle.fill | 🔵 Blue | ≥60% in target zone |
| Fair | ➖ minus.circle.fill | 🟠 Orange | ≥40% in target zone |
| Poor | ❌ xmark.circle.fill | 🔴 Red | <40% in target zone |
| None | ❓ questionmark.circle.fill | ⚫ Gray | No data |

---

## 📊 Chart Data Structures

### ZoneDataPoint
**Purpose**: Zone performance timeline visualization
```swift
struct ZoneDataPoint {
    let time: Double           // Time offset in seconds
    let zone: PressureZone     // Therapeutic zone
    let pressure: Double       // Raw pressure (not displayed)
    let step: Int             // Exhalation number
    
    var zoneValue: Double {   // Y-axis mapping
        switch zone {
        case .red: return 1    // Above Zone
        case .amber: return 2  // Below Zone  
        case .green: return 3  // In Zone
        }
    }
    
    var isBreathStart: Bool {  // Breath markers
        return Int(time) % 4 == 0 && time > 0
    }
}
```

### PressureZoneDataPoint
**Purpose**: Pressure timeline with zone-based coloring
```swift
struct PressureZoneDataPoint {
    let time: Double
    let pressure: Double
    let step: Int
    let zone: PressureZone
}
```

### ExhalationDataPoint
**Purpose**: Duration compliance bar charts
```swift
struct ExhalationDataPoint {
    let exhalationNumber: Int
    let duration: Double
    let isOptimal: Bool        // ≥3.0 seconds
}
```

---

## ⏰ Time-Based Filtering

### Timeframe Options
```swift
enum TimeFrame: String, CaseIterable {
    case day = "Today"
    case week = "This Week"
    case month = "This Month"
    case year = "This Year"
}
```

### Filtering Logic
```swift
var filteredSessions: [CompletedSession] {
    let calendar = Calendar.current
    let now = Date()
    
    return historyManager.sessions.filter { session in
        switch selectedTimeframe {
        case .day:
            return calendar.isDate(session.date, inSameDayAs: now)
        case .week:
            return calendar.isDate(session.date, equalTo: now, toGranularity: .weekOfYear)
        case .month:
            return calendar.isDate(session.date, equalTo: now, toGranularity: .month)
        case .year:
            return calendar.isDate(session.date, equalTo: now, toGranularity: .year)
        }
    }
}
```

---

## 🏗️ Information Hierarchy

### Level 1: History Tab Main View
```
📱 History Tab
├── 📊 Adherence Overview Section
│   ├── 🎯 Overall Adherence Score (%)
│   ├── 📅 Sessions Completed vs Planned
│   ├── ⭐ Average Quality Rating
│   └── 📈 7-Day Compliance Trend Chart
├── 🕒 Time Filter Navigation
│   ├── 📅 Today
│   ├── 📆 This Week  
│   ├── 🗓️ This Month
│   └── 📋 This Year
└── 📋 Sessions List (Grouped by Date)
    ├── 🗂️ Session Cards per Day
    │   ├── 🎯 Quality Indicator (color-coded)
    │   ├── ⏰ Time of Session
    │   ├── ⏱️ Duration
    │   ├── 🫁 Steps Completed/Total
    │   └── 👆 Tap for Details
    └── 📂 Expandable Date Sections
```

### Level 2: Session Detail View - Four-Tab Structure
```
📱 Session Detail View
├── 📋 Session Header
│   ├── 📅 Date & Time
│   ├── 🏆 Quality Badge
│   └── 📊 Key Metrics (Duration, Exhalations, Target Effort %)
├── 🗂️ Four-Tab Interface
│   ├── 📖 Overview Tab
│   │   ├── 📊 Adherence Summary
│   │   │   ├── ✅ Exhalations Completed
│   │   │   └── 🎯 Quality Exhalations
│   │   ├── 💪 Effort Compliance Overview
│   │   │   ├── 🎯 Predominant Zone
│   │   │   ├── 📈 Zone Consistency
│   │   │   └── ⏱️ Time in Target Zone
│   │   ├── ⏰ Time Compliance Overview
│   │   │   ├── ⏱️ Average Duration
│   │   │   └── ✅ Optimal Duration Count
│   │   └── 💬 Session Feedback
│   ├── 📊 Adherence Tab
│   │   └── 📋 Adherence Metrics Grid
│   │       ├── ✅ Session Completion
│   │       ├── ⭐ Quality Achievement
│   │       ├── ⏰ Duration Target
│   │       └── 🎯 Effort Compliance
│   ├── 💪 Effort Tab
│   │   ├── 📈 Pressure Timeline Chart
│   │   │   ├── 🌈 Zone-colored line graph
│   │   │   ├── 🎨 Background therapeutic zones
│   │   │   └── 📍 Breath markers
│   │   └── 📊 Effort Zone Analysis
│   │       ├── ⏱️ Time in Target Zone
│   │       ├── 📈 Zone Consistency
│   │       └── 🎯 Predominant Zone
│   └── ⏰ Time Tab
│       └── 📊 Exhalation Duration Chart
│           ├── 📊 Bar chart per exhalation
│           ├── 🎨 Color-coded by compliance (≥3.0s)
│           └── 📈 Summary Metrics
│               ├── ✅ Optimal Duration Count
│               ├── ⏱️ Average Duration
│               └── 📊 Consistency Rating
```

---

## 🎨 Design Principles

### ✅ Therapeutic Focus
- **No Raw Pressure Values**: All metrics use therapeutic zones
- **Effort Terminology**: "Exhalation Effort" instead of pressure
- **Compliance-Centered**: Focus on adherence and zone performance
- **Time-Based Analysis**: Duration compliance vs. 3-second targets

### ✅ Visual Design
- **Apple HIG Compliance**: Native iOS design patterns
- **Dark Theme**: Professional gradient backgrounds
- **Color-Coded Performance**: Intuitive green/yellow/orange/red system
- **Modern Charts**: Clean, elegant visualizations

### ✅ Accessibility
- **Dynamic Type**: Text scales with user preferences
- **VoiceOver**: Proper accessibility labels
- **Color Contrast**: WCAG compliant combinations
- **Reduced Motion**: Respects user motion preferences

---

## 🔮 Future Enhancements

### Data Collection Improvements
- **Real Breath Timing**: Replace estimated durations with actual breath detection
- **Enhanced Zone Tracking**: More granular zone transition analysis
- **Pressure Variability**: Standard deviation and consistency metrics

### Advanced Analytics
- **Trend Analysis**: Long-term progression tracking
- **Predictive Insights**: Session quality prediction
- **Comparative Analysis**: Performance vs. therapy goals

### User Experience
- **Export Capabilities**: PDF reports for healthcare providers
- **Goal Setting**: Personalized targets and milestones
- **Social Features**: Progress sharing and motivation

---

*This documentation reflects the current implementation of the Breath2 History Tab as of the latest codebase analysis. All calculations and data structures are based on the actual Swift code implementation.*
